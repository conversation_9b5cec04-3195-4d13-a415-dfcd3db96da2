<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToFailedJobsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \Illuminate\Support\Facades\DB::table('failed_jobs')->truncate();
        Schema::table('failed_jobs', function (Blueprint $table) {
            if (!Schema::hasColumn('failed_jobs', 'uuid')) {
                $table->string('uuid')->unique()->after('id');
            }
            if (!Schema::hasColumn('failed_jobs', 'exception')) {
                $table->longText('exception')->after('payload');
            }
            if (!Schema::hasColumn('failed_jobs', 'failed_at')) {
                $table->timestamp('failed_at')->useCurrent();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('failed_jobs', function (Blueprint $table) {
            $table->dropColumn('uuid');
            $table->dropColumn('exception');
        });
    }
}
