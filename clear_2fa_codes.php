<?php

// <PERSON>ript to clear any existing 2FA codes for local development
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Clearing 2FA codes for local development...\n";

try {
    // Clear all 2FA codes from users
    $updated = DB::table('users')->update([
        'two_factor_code' => null,
        'two_factor_expires_at' => null
    ]);
    
    echo "✓ Cleared 2FA codes for {$updated} users\n";
    
    // Check current environment and 2FA setting
    echo "\nCurrent configuration:\n";
    echo "- APP_ENV: " . config('app.env') . "\n";
    echo "- TWO_FACTOR_ENABLED: " . (config('auth.two_factor_enabled') ? 'true' : 'false') . "\n";
    
    echo "\n2FA should now be disabled for local development.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "This might be due to database connection issues or missing migrations.\n";
}
