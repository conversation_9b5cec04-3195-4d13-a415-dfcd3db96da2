-- Anonymize personal data in the database after import

-- Anonymize customer emails
UPDATE customers SET email = CONCAT('customer', id, '@example.com') WHERE email IS NOT NULL;

-- Anonymize customer phone numbers
UPDATE customers SET phone = '************' WHERE phone IS NOT NULL;

-- Anonymize customer addresses
UPDATE customers SET address = '123 Anonymous Street' WHERE address IS NOT NULL;
UPDATE customers SET city = 'Anonymous City' WHERE city IS NOT NULL;
UPDATE customers SET postal_code = '12345' WHERE postal_code IS NOT NULL;

-- Anonymize customer names
UPDATE customers SET first_name = 'John' WHERE first_name IS NOT NULL;
UPDATE customers SET last_name = 'Doe' WHER<PERSON> last_name IS NOT NULL;

-- Anonymize user emails (if any personal users exist)
UPDATE users SET email = CONCAT('user', id, '@example.com') WHERE email IS NOT NULL AND email != '<EMAIL>';

-- Anonymize any other tables that might contain personal data
-- Check if reservations table has personal data
UPDATE reservations SET customer_email = CONCAT('customer', id, '@example.com') WHERE customer_email IS NOT NULL;
UPDATE reservations SET customer_phone = '************' WHERE customer_phone IS NOT NULL;

-- Anonymize contact leads if they exist
UPDATE contact_leads SET email = CONCAT('lead', id, '@example.com') WHERE email IS NOT NULL;
UPDATE contact_leads SET phone = '************' WHERE phone IS NOT NULL;
UPDATE contact_leads SET name = 'Anonymous Lead' WHERE name IS NOT NULL;

-- Anonymize any feedback emails
UPDATE feedbacks SET email = CONCAT('feedback', id, '@example.com') WHERE email IS NOT NULL;

-- Show anonymization results
SELECT 'Customers anonymized' as table_name, COUNT(*) as count FROM customers;
SELECT 'Users anonymized' as table_name, COUNT(*) as count FROM users WHERE email LIKE '%@example.com';
SELECT 'Reservations anonymized' as table_name, COUNT(*) as count FROM reservations WHERE customer_email LIKE '%@example.com';
SELECT 'Contact leads anonymized' as table_name, COUNT(*) as count FROM contact_leads WHERE email LIKE '%@example.com';
SELECT 'Feedbacks anonymized' as table_name, COUNT(*) as count FROM feedbacks WHERE email LIKE '%@example.com';
