<?php

namespace Tests\Feature\Hodor;

use App\LocalisedMotif;
use App\Post;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PostsControllerLocalisedMotifsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for authentication
        $this->user = User::factory()->create([
            'role' => 'super-admin'
        ]);
    }

    /**
     * Test that LocalisedMotifs are loaded in the edit form.
     */
    public function test_localised_motifs_are_loaded_in_edit_form(): void
    {
        // Create test data
        $post = Post::factory()->create([
            'published' => true,
            'featured' => false
        ]);

        $localisedMotif1 = LocalisedMotif::create([
            'title' => 'Heraklion',
            'description' => 'Capital of Crete'
        ]);

        $localisedMotif2 = LocalisedMotif::create([
            'title' => 'Chania',
            'description' => 'Beautiful coastal city'
        ]);

        // Make authenticated request to edit page
        $response = $this->actingAs($this->user)
            ->get(route('hodor.posts.edit', $post->id));

        $response->assertStatus(200);
        $response->assertViewHas('localised_motifs');
        
        // Check that localised motifs are in the view data
        $viewData = $response->viewData('localised_motifs');
        $this->assertArrayHasKey($localisedMotif1->id, $viewData);
        $this->assertArrayHasKey($localisedMotif2->id, $viewData);
        $this->assertEquals('Heraklion', $viewData[$localisedMotif1->id]);
        $this->assertEquals('Chania', $viewData[$localisedMotif2->id]);
    }

    /**
     * Test that LocalisedMotifs can be synced when updating a post.
     */
    public function test_localised_motifs_can_be_synced_when_updating_post(): void
    {
        // Create test data
        $post = Post::factory()->create([
            'published' => true,
            'featured' => false
        ]);

        $localisedMotif1 = LocalisedMotif::create([
            'title' => 'Heraklion',
            'description' => 'Capital of Crete'
        ]);

        $localisedMotif2 = LocalisedMotif::create([
            'title' => 'Chania',
            'description' => 'Beautiful coastal city'
        ]);

        // Prepare update data
        $updateData = [
            'featured' => '1',
            'published' => '1',
            'motifs' => [],
            'tags' => [],
            'localised_motifs' => [$localisedMotif1->id, $localisedMotif2->id],
            'en' => [
                'title' => 'Test Post Title',
                'content' => 'Test post content',
                'meta_title' => 'Test Meta Title',
                'meta_description' => 'Test meta description'
            ]
        ];

        // Make authenticated request to update post
        $response = $this->actingAs($this->user)
            ->put(route('hodor.posts.update', $post->id), $updateData);

        $response->assertRedirect(route('hodor.posts.edit', $post->id));
        $response->assertSessionHas('success');

        // Verify that localised motifs were synced
        $post->refresh();
        $this->assertCount(2, $post->localisedMotifs);
        $this->assertTrue($post->localisedMotifs->contains($localisedMotif1));
        $this->assertTrue($post->localisedMotifs->contains($localisedMotif2));
    }

    /**
     * Test that LocalisedMotifs can be removed when updating a post.
     */
    public function test_localised_motifs_can_be_removed_when_updating_post(): void
    {
        // Create test data
        $post = Post::factory()->create([
            'published' => true,
            'featured' => false
        ]);

        $localisedMotif1 = LocalisedMotif::create([
            'title' => 'Heraklion',
            'description' => 'Capital of Crete'
        ]);

        $localisedMotif2 = LocalisedMotif::create([
            'title' => 'Chania',
            'description' => 'Beautiful coastal city'
        ]);

        // Initially attach both localised motifs
        $post->localisedMotifs()->attach([$localisedMotif1->id, $localisedMotif2->id]);
        $this->assertCount(2, $post->localisedMotifs);

        // Prepare update data with only one localised motif
        $updateData = [
            'featured' => '1',
            'published' => '1',
            'motifs' => [],
            'tags' => [],
            'localised_motifs' => [$localisedMotif1->id], // Only one motif
            'en' => [
                'title' => 'Test Post Title',
                'content' => 'Test post content',
                'meta_title' => 'Test Meta Title',
                'meta_description' => 'Test meta description'
            ]
        ];

        // Make authenticated request to update post
        $response = $this->actingAs($this->user)
            ->put(route('hodor.posts.update', $post->id), $updateData);

        $response->assertRedirect(route('hodor.posts.edit', $post->id));

        // Verify that only one localised motif remains
        $post->refresh();
        $this->assertCount(1, $post->localisedMotifs);
        $this->assertTrue($post->localisedMotifs->contains($localisedMotif1));
        $this->assertFalse($post->localisedMotifs->contains($localisedMotif2));
    }

    /**
     * Test that empty LocalisedMotifs array removes all associations.
     */
    public function test_empty_localised_motifs_array_removes_all_associations(): void
    {
        // Create test data
        $post = Post::factory()->create([
            'published' => true,
            'featured' => false
        ]);

        $localisedMotif = LocalisedMotif::create([
            'title' => 'Heraklion',
            'description' => 'Capital of Crete'
        ]);

        // Initially attach localised motif
        $post->localisedMotifs()->attach($localisedMotif->id);
        $this->assertCount(1, $post->localisedMotifs);

        // Prepare update data with empty localised motifs
        $updateData = [
            'featured' => '1',
            'published' => '1',
            'motifs' => [],
            'tags' => [],
            'localised_motifs' => [], // Empty array
            'en' => [
                'title' => 'Test Post Title',
                'content' => 'Test post content',
                'meta_title' => 'Test Meta Title',
                'meta_description' => 'Test meta description'
            ]
        ];

        // Make authenticated request to update post
        $response = $this->actingAs($this->user)
            ->put(route('hodor.posts.update', $post->id), $updateData);

        $response->assertRedirect(route('hodor.posts.edit', $post->id));

        // Verify that all localised motifs were removed
        $post->refresh();
        $this->assertCount(0, $post->localisedMotifs);
    }
}
